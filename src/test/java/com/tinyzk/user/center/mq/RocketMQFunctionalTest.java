package com.tinyzk.user.center.mq;

import com.tinyzk.user.center.service.MigratedMessageProducerService;
import lombok.extern.slf4j.Slf4j;
import com.tinyzk.user.center.dto.SendResultAdapter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * RocketMQ功能测试
 */
@SpringBootTest
@ActiveProfiles("local")
@Slf4j
public class RocketMQFunctionalTest {

    @Autowired
    private MigratedMessageProducerService messageProducerService;

    @Test
    public void testResumeParseMessageSend() throws Exception {
        log.info("=== 开始测试简历解析消息发送 ===");
        
        // 构造测试消息
        Map<String, Object> message = new HashMap<>();
        message.put("content", "测试简历解析消息");
        message.put("timestamp", LocalDateTime.now());
        message.put("testType", "RESUME_PARSE_TEST");
        message.put("userId", 12345L);
        
        try {
            // 发送消息
            messageProducerService.sendResumeParseMessage(message);
            log.info("简历解析消息发送成功: {}", message);
            
            // 等待消息被消费
            Thread.sleep(2000);
            
        } catch (Exception e) {
            log.error("简历解析消息发送失败", e);
            throw e;
        }
        
        log.info("=== 简历解析消息发送测试完成 ===");
    }

    @Test
    public void testFileUploadMessageSend() throws Exception {
        log.info("=== 开始测试文件上传消息发送 ===");
        
        // 构造测试消息
        Map<String, Object> message = new HashMap<>();
        message.put("fileName", "test-resume.pdf");
        message.put("fileSize", 1024000);
        message.put("timestamp", LocalDateTime.now());
        message.put("testType", "FILE_UPLOAD_TEST");
        message.put("uploadPath", "/tmp/test-resume.pdf");
        
        try {
            // 发送消息
            messageProducerService.sendFileUploadMessage(message);
            log.info("文件上传消息发送成功: {}", message);
            
            // 等待消息被消费
            Thread.sleep(2000);
            
        } catch (Exception e) {
            log.error("文件上传消息发送失败", e);
            throw e;
        }
        
        log.info("=== 文件上传消息发送测试完成 ===");
    }

    @Test
    public void testGeneralMessageSend() throws Exception {
        log.info("=== 开始测试通用消息发送 ===");
        
        String topic = "TEST_TOPIC";
        String tag = "TEST_TAG";
        
        // 构造测试消息
        Map<String, Object> message = new HashMap<>();
        message.put("content", "测试通用消息");
        message.put("timestamp", LocalDateTime.now());
        message.put("testType", "GENERAL_TEST");
        message.put("messageId", "MSG_" + System.currentTimeMillis());
        
        try {
            // 发送消息
            messageProducerService.sendGeneralMessage(topic, tag, message);
            log.info("通用消息发送成功: topic={}, tag={}, message={}", topic, tag, message);
            
            // 等待消息被消费
            Thread.sleep(2000);
            
        } catch (Exception e) {
            log.error("通用消息发送失败", e);
            throw e;
        }
        
        log.info("=== 通用消息发送测试完成 ===");
    }

    @Test
    public void testSyncMessageSend() throws Exception {
        log.info("=== 开始测试同步消息发送 ===");
        
        String topic = "SYNC_TEST_TOPIC";
        String tag = "SYNC_TAG";
        
        // 构造测试消息
        Map<String, Object> message = new HashMap<>();
        message.put("content", "测试同步消息");
        message.put("timestamp", LocalDateTime.now());
        message.put("testType", "SYNC_TEST");
        message.put("messageId", "SYNC_MSG_" + System.currentTimeMillis());
        
        try {
            // 同步发送消息
            SendResultAdapter sendResult = messageProducerService.sendMessageSync(topic, tag, message);
            log.info("同步消息发送成功: topic={}, tag={}, result={}", topic, tag, sendResult);
            
            // 验证发送结果
            assert sendResult != null;
            assert sendResult.getMsgId() != null;
            log.info("消息ID: {}, 队列ID: {}, 队列偏移量: {}, 发送状态: {}", 
                sendResult.getMsgId(), 
                sendResult.getMessageQueue().getQueueId(),
                sendResult.getQueueOffset(),
                sendResult.getSendStatus());
            
        } catch (Exception e) {
            log.error("同步消息发送失败", e);
            throw e;
        }
        
        log.info("=== 同步消息发送测试完成 ===");
    }

    @Test
    public void testBatchMessageSend() throws Exception {
        log.info("=== 开始测试批量消息发送 ===");
        
        int messageCount = 5;
        
        for (int i = 0; i < messageCount; i++) {
            // 构造测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("content", "批量测试消息 #" + (i + 1));
            message.put("timestamp", LocalDateTime.now());
            message.put("testType", "BATCH_TEST");
            message.put("batchIndex", i + 1);
            message.put("totalCount", messageCount);
            
            try {
                // 发送消息
                messageProducerService.sendResumeParseMessage(message);
                log.info("批量消息 #{} 发送成功: {}", i + 1, message);
                
                // 短暂延迟
                Thread.sleep(100);
                
            } catch (Exception e) {
                log.error("批量消息 #{} 发送失败", i + 1, e);
                throw e;
            }
        }
        
        // 等待所有消息被消费
        Thread.sleep(3000);
        
        log.info("=== 批量消息发送测试完成，共发送 {} 条消息 ===", messageCount);
    }

    @Test
    public void testMessageProducerPerformance() throws Exception {
        log.info("=== 开始测试消息生产者性能 ===");
        
        int messageCount = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < messageCount; i++) {
            // 构造测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("content", "性能测试消息 #" + (i + 1));
            message.put("timestamp", LocalDateTime.now());
            message.put("testType", "PERFORMANCE_TEST");
            message.put("messageIndex", i + 1);
            
            try {
                // 发送消息
                messageProducerService.sendResumeParseMessage(message);
                
                if ((i + 1) % 20 == 0) {
                    log.info("已发送 {} 条消息", i + 1);
                }
                
            } catch (Exception e) {
                log.error("性能测试消息 #{} 发送失败", i + 1, e);
                throw e;
            }
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double throughput = (double) messageCount / duration * 1000;
        
        log.info("=== 性能测试完成 ===");
        log.info("总消息数: {}", messageCount);
        log.info("总耗时: {} ms", duration);
        log.info("吞吐量: {:.2f} 消息/秒", throughput);
        
        // 等待消息被消费
        Thread.sleep(5000);
    }

    @Test
    public void testMessageProducerStability() throws Exception {
        log.info("=== 开始测试消息生产者稳定性 ===");
        
        int rounds = 3;
        int messagesPerRound = 10;
        
        for (int round = 0; round < rounds; round++) {
            log.info("开始第 {} 轮稳定性测试", round + 1);
            
            for (int i = 0; i < messagesPerRound; i++) {
                // 构造测试消息
                Map<String, Object> message = new HashMap<>();
                message.put("content", String.format("稳定性测试消息 Round-%d #%d", round + 1, i + 1));
                message.put("timestamp", LocalDateTime.now());
                message.put("testType", "STABILITY_TEST");
                message.put("round", round + 1);
                message.put("messageIndex", i + 1);
                
                try {
                    // 发送消息
                    messageProducerService.sendResumeParseMessage(message);
                    
                } catch (Exception e) {
                    log.error("稳定性测试消息 Round-{} #{} 发送失败", round + 1, i + 1, e);
                    throw e;
                }
            }
            
            log.info("第 {} 轮测试完成，发送 {} 条消息", round + 1, messagesPerRound);
            
            // 轮次间隔
            if (round < rounds - 1) {
                Thread.sleep(1000);
            }
        }
        
        // 等待所有消息被消费
        Thread.sleep(3000);
        
        log.info("=== 稳定性测试完成，共 {} 轮，每轮 {} 条消息 ===", rounds, messagesPerRound);
    }
}
