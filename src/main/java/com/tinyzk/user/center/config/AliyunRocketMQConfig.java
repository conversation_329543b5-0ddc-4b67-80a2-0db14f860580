package com.tinyzk.user.center.config;

import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Properties;

/**
 * 阿里云RocketMQ配置类
 * 基于官方TCP示例的最佳实践
 */
@Configuration
@Slf4j
public class AliyunRocketMQConfig {

    @Value("${rocketmq.name-server}")
    private String nameServer;

    @Value("${rocketmq.producer.access-key}")
    private String accessKey;

    @Value("${rocketmq.producer.secret-key}")
    private String secretKey;

    @Value("${rocketmq.producer.group}")
    private String producerGroup;

    @Value("${rocketmq.consumer.group}")
    private String consumerGroup;

    /**
     * 创建阿里云RocketMQ生产者
     * 基于官方Demo的配置方式
     */
    @Bean("aliyunProducer")
    @Primary
    public Producer aliyunProducer() {
        log.info("初始化阿里云RocketMQ生产者...");
        log.info("配置信息 - nameServer: {}, producerGroup: {}, accessKey: {}", 
                nameServer, producerGroup, accessKey);

        Properties producerProperties = new Properties();
        // 按照官方Demo的方式设置属性
        producerProperties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        producerProperties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        producerProperties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        producerProperties.setProperty(PropertyKeyConst.GROUP_ID, producerGroup);

        try {
            Producer producer = ONSFactory.createProducer(producerProperties);
            producer.start();
            log.info("阿里云RocketMQ生产者启动成功");
            return producer;
        } catch (Exception e) {
            log.error("阿里云RocketMQ生产者启动失败", e);
            throw new RuntimeException("RocketMQ生产者启动失败", e);
        }
    }

    /**
     * 创建阿里云RocketMQ消费者
     * 基于官方Demo的配置方式
     */
    @Bean("aliyunConsumer")
    public Consumer aliyunConsumer() {
        log.info("初始化阿里云RocketMQ消费者...");
        log.info("配置信息 - nameServer: {}, consumerGroup: {}, accessKey: {}", 
                nameServer, consumerGroup, accessKey);

        Properties consumerProperties = new Properties();
        // 按照官方Demo的方式设置属性
        consumerProperties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        consumerProperties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        consumerProperties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        consumerProperties.setProperty(PropertyKeyConst.GROUP_ID, consumerGroup);

        try {
            Consumer consumer = ONSFactory.createConsumer(consumerProperties);
            log.info("阿里云RocketMQ消费者创建成功，需要手动订阅Topic并启动");
            return consumer;
        } catch (Exception e) {
            log.error("阿里云RocketMQ消费者创建失败", e);
            throw new RuntimeException("RocketMQ消费者创建失败", e);
        }
    }

    /**
     * 获取RocketMQ通用属性配置
     * 供其他组件使用
     */
    public Properties getRocketMQProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        return properties;
    }
}
