package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;

/**
 * RocketMQ健康检查
 * 基于阿里云RocketMQ最佳实践指南
 * 注意：已暂时禁用，等待迁移到阿里云客户端完成后重新启用
 */
@Slf4j
public class RocketMQHealthIndicator {

    // 整个类暂时禁用，避免编译错误
    // 等待阿里云RocketMQ迁移完成后重新实现

    /*
    @Component
    public class RocketMQHealthIndicator implements HealthIndicator {

        private final AliyunMessageService aliyunMessageService;

        public RocketMQHealthIndicator(AliyunMessageService aliyunMessageService) {
            this.aliyunMessageService = aliyunMessageService;
        }

        @Override
        public Health health() {
            try {
                boolean isHealthy = aliyunMessageService.isProducerRunning();
                if (isHealthy) {
                    return Health.up()
                        .withDetail("status", "RocketMQ连接正常")
                        .build();
                } else {
                    return Health.down()
                        .withDetail("status", "RocketMQ连接异常")
                        .build();
                }
            } catch (Exception e) {
                return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
            }
        }
    }
    */
}
