package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ消费者配置
 * 基于阿里云RocketMQ最佳实践指南
 * 注意：此配置已被AliyunRocketMQConfig替代，暂时禁用
 */
//@Configuration
@Slf4j
public class RocketMQConsumerConfig {

    @Value("${rocketmq.name-server:localhost:9876}")
    private String nameServer;

    @Value("${rocketmq.consumer.group:user-center-consumer-group}")
    private String consumerGroup;

    @Value("${rocketmq.consumer.access-key:}")
    private String accessKey;

    @Value("${rocketmq.consumer.secret-key:}")
    private String secretKey;

    /**
     * 简历解析消费者配置
     * 基于最佳实践指南的推荐配置
     */
    @Bean("resumeParseConsumer")
    public DefaultMQPushConsumer resumeParseConsumer() {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(consumerGroup);

        try {
            // 基础配置
            consumer.setNamesrvAddr(nameServer);
            consumer.setInstanceName("resume-parse-consumer-instance-" + System.currentTimeMillis());

            // 阿里云RocketMQ认证配置
            if (!accessKey.isEmpty() && !secretKey.isEmpty()) {
                System.setProperty("rocketmq.client.accessKey", accessKey);
                System.setProperty("rocketmq.client.secretKey", secretKey);
                log.info("配置阿里云RocketMQ Consumer认证信息");
            }
            
            // 消费模式配置 - 基于最佳实践指南
            consumer.setMessageModel(MessageModel.CLUSTERING); // 集群消费模式
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET); // 从最后偏移量开始消费
            
            // 性能优化配置 - 参考最佳实践指南
            consumer.setConsumeThreadMin(5);  // 最小消费线程数
            consumer.setConsumeThreadMax(20); // 最大消费线程数
            consumer.setConsumeMessageBatchMaxSize(1); // 批量消费消息数量
            consumer.setPullBatchSize(32); // 批量拉取消息数量
            
            // 超时和重试配置 - 基于最佳实践指南
            consumer.setConsumeTimeout(15); // 消费超时时间（分钟）
            consumer.setMaxReconsumeTimes(3); // 最大重试次数
            
            // 订阅Topic - 这里先不启动，由注解方式的消费者处理
            // consumer.subscribe("RESUME_PARSE_TOPIC", "*");
            
            log.info("RocketMQ简历解析消费者配置完成: nameServer={}, group={}", 
                    nameServer, "resume-parse-consumer-group");
                    
        } catch (Exception e) {
            log.error("RocketMQ简历解析消费者配置失败", e);
            throw new RuntimeException("RocketMQ消费者配置失败", e);
        }

        return consumer;
    }

    /**
     * 通用消费者配置
     */
    @Bean("generalConsumer")
    public DefaultMQPushConsumer generalConsumer() {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(consumerGroup);

        try {
            // 基础配置
            consumer.setNamesrvAddr(nameServer);
            consumer.setInstanceName("general-consumer-instance-" + System.currentTimeMillis());

            // 阿里云RocketMQ认证配置
            if (!accessKey.isEmpty() && !secretKey.isEmpty()) {
                System.setProperty("rocketmq.client.accessKey", accessKey);
                System.setProperty("rocketmq.client.secretKey", secretKey);
            }
            
            // 消费模式配置
            consumer.setMessageModel(MessageModel.CLUSTERING);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            
            // 性能配置
            consumer.setConsumeThreadMin(10);
            consumer.setConsumeThreadMax(30);
            consumer.setConsumeMessageBatchMaxSize(1);
            consumer.setPullBatchSize(32);
            
            // 超时和重试配置
            consumer.setConsumeTimeout(15);
            consumer.setMaxReconsumeTimes(3);
            
            log.info("RocketMQ通用消费者配置完成: nameServer={}, group={}", 
                    nameServer, consumerGroup);
                    
        } catch (Exception e) {
            log.error("RocketMQ通用消费者配置失败", e);
            throw new RuntimeException("RocketMQ消费者配置失败", e);
        }

        return consumer;
    }
}
