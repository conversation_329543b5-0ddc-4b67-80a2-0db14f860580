package com.tinyzk.user.center.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.SendResultAdapter;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
// import org.apache.rocketmq.client.producer.SendResult; // 注释掉，因为已切换到阿里云客户端
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.UUID;

/**
 * 迁移后的消息生产服务
 * 基于阿里云官方客户端，保持与原MessageProducerService相同的接口
 */
@Service
@Slf4j
public class MigratedMessageProducerService {

    private final AliyunMessageService aliyunMessageService;
    private final RateLimiter producerLimiter;
    private final MeterRegistry meterRegistry;
    private final ObjectMapper objectMapper;

    // 主题定义 - 保持与原服务一致
    public static final String RESUME_PARSE_TOPIC = "RESUME_PARSE_TOPIC";
    public static final String FILE_UPLOAD_TOPIC = "FILE_UPLOAD_TOPIC";
    public static final String GENERAL_TOPIC = "GENERAL_TOPIC";

    public MigratedMessageProducerService(AliyunMessageService aliyunMessageService,
                                         MeterRegistry meterRegistry) {
        this.aliyunMessageService = aliyunMessageService;
        this.meterRegistry = meterRegistry;
        this.objectMapper = new ObjectMapper();
        
        // 使用Resilience4j的RateLimiter，限制生产速度
        this.producerLimiter = RateLimiter.of("message-producer",
            RateLimiterConfig.custom()
                .limitForPeriod(50) // 每个周期50个请求
                .limitRefreshPeriod(Duration.ofSeconds(1)) // 1秒刷新周期
                .timeoutDuration(Duration.ofMillis(100)) // 100ms超时
                .build());
    }

    /**
     * 发送简历解析消息
     * 保持与原MessageProducerService相同的接口
     */
    public void sendResumeParseMessage(Object message) {
        // 使用Resilience4j RateLimiter
        Boolean result = producerLimiter.executeSupplier(() -> {
            return sendResumeParseMessageInternal(message);
        });

        if (result == null || !result) {
            throw new RuntimeException("消息发送频率过高，请稍后重试");
        }
    }

    /**
     * 发送文件上传消息
     */
    public void sendFileUploadMessage(Object message) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            String messageBody = convertToJsonString(message);
            boolean success = aliyunMessageService.sendFileUploadMessage(messageBody);
            
            if (success) {
                meterRegistry.counter("mq.message.send.success", "topic", FILE_UPLOAD_TOPIC).increment();
                log.debug("文件上传消息发送成功: {}", messageBody);
            } else {
                meterRegistry.counter("mq.message.send.failure", "topic", FILE_UPLOAD_TOPIC).increment();
                throw new RuntimeException("文件上传消息发送失败");
            }
            
        } catch (Exception e) {
            meterRegistry.counter("mq.message.send.error", "topic", FILE_UPLOAD_TOPIC).increment();
            log.error("发送文件上传消息异常", e);
            throw e;
        } finally {
            sample.stop(Timer.builder("mq.message.send.duration")
                .tag("topic", FILE_UPLOAD_TOPIC)
                .register(meterRegistry));
        }
    }

    /**
     * 发送通用消息
     */
    public void sendGeneralMessage(String topic, String tag, Object message) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            String messageBody = convertToJsonString(message);
            // 使用阿里云消息服务发送，但需要适配topic和tag
            boolean success = sendGeneralMessageInternal(topic, tag, messageBody);
            
            if (success) {
                meterRegistry.counter("mq.message.send.success", "topic", topic).increment();
                log.debug("通用消息发送成功: topic={}, tag={}", topic, tag);
            } else {
                meterRegistry.counter("mq.message.send.failure", "topic", topic).increment();
                throw new RuntimeException("通用消息发送失败");
            }
            
        } catch (Exception e) {
            meterRegistry.counter("mq.message.send.error", "topic", topic).increment();
            log.error("发送通用消息异常: topic={}, tag={}", topic, tag, e);
            throw e;
        } finally {
            sample.stop(Timer.builder("mq.message.send.duration")
                .tag("topic", topic)
                .register(meterRegistry));
        }
    }

    /**
     * 同步发送消息
     * 模拟原SendResult返回
     */
    public SendResultAdapter sendMessageSync(String topic, String tag, Object message) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            String messageBody = convertToJsonString(message);
            boolean success = sendGeneralMessageInternal(topic, tag, messageBody);
            
            if (success) {
                meterRegistry.counter("mq.message.send.sync.success", "topic", topic).increment();
                
                // 创建模拟的SendResult
                return createMockSendResult(topic);
            } else {
                meterRegistry.counter("mq.message.send.sync.failure", "topic", topic).increment();
                throw new RuntimeException("同步消息发送失败");
            }
            
        } catch (Exception e) {
            meterRegistry.counter("mq.message.send.sync.error", "topic", topic).increment();
            log.error("同步发送消息异常: topic={}, tag={}", topic, tag, e);
            throw e;
        } finally {
            sample.stop(Timer.builder("mq.message.send.sync.duration")
                .tag("topic", topic)
                .register(meterRegistry));
        }
    }

    /**
     * 内部方法：发送简历解析消息
     */
    private boolean sendResumeParseMessageInternal(Object message) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            String messageBody = convertToJsonString(message);
            boolean success = aliyunMessageService.sendResumeParseMessage(messageBody);
            
            if (success) {
                meterRegistry.counter("mq.message.send.success", "topic", RESUME_PARSE_TOPIC).increment();
                log.debug("简历解析消息发送成功: {}", messageBody);
            } else {
                meterRegistry.counter("mq.message.send.failure", "topic", RESUME_PARSE_TOPIC).increment();
            }
            
            return success;
            
        } catch (Exception e) {
            meterRegistry.counter("mq.message.send.error", "topic", RESUME_PARSE_TOPIC).increment();
            log.error("发送简历解析消息异常", e);
            return false;
        } finally {
            sample.stop(Timer.builder("mq.message.send.duration")
                .tag("topic", RESUME_PARSE_TOPIC)
                .register(meterRegistry));
        }
    }

    /**
     * 内部方法：发送通用消息
     */
    private boolean sendGeneralMessageInternal(String topic, String tag, String messageBody) {
        // 根据topic路由到不同的发送方法
        switch (topic) {
            case RESUME_PARSE_TOPIC:
                return aliyunMessageService.sendResumeParseMessage(messageBody);
            case FILE_UPLOAD_TOPIC:
                return aliyunMessageService.sendFileUploadMessage(messageBody);
            case "TEST_TOPIC":
                return aliyunMessageService.sendTestMessage(messageBody);
            default:
                // 对于其他topic，使用测试消息发送
                log.warn("未知的topic: {}, 使用测试消息发送", topic);
                return aliyunMessageService.sendTestMessage(messageBody);
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJsonString(Object message) {
        if (message instanceof String) {
            return (String) message;
        } else {
            try {
                return objectMapper.writeValueAsString(message);
            } catch (Exception e) {
                log.error("转换对象为JSON字符串失败", e);
                return message.toString();
            }
        }
    }

    /**
     * 创建模拟的SendResult
     */
    private SendResultAdapter createMockSendResult(String topic) {
        // 使用适配器类来包装结果
        try {
            String msgId = UUID.randomUUID().toString();
            return new SendResultAdapter(msgId, topic);
        } catch (Exception e) {
            log.warn("创建模拟SendResult失败，返回null", e);
            return null;
        }
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
