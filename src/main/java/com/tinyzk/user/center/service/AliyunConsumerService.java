package com.tinyzk.user.center.service;

import com.aliyun.openservices.ons.api.Consumer;
import com.tinyzk.user.center.consumer.AliyunMessageListener;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;



/**
 * 阿里云RocketMQ消费者管理服务
 * 基于官方TCP示例的最佳实践
 */
@Service
@Slf4j
public class AliyunConsumerService {

    private final Consumer consumer;
    private final AliyunMessageListener messageListener;

    public AliyunConsumerService(@Qualifier("aliyunConsumer") Consumer consumer,
                                AliyunMessageListener messageListener) {
        this.consumer = consumer;
        this.messageListener = messageListener;
    }

    /**
     * 应用启动时初始化消费者
     * 按照官方Demo的方式订阅Topic
     */
    @PostConstruct
    public void initConsumer() {
        try {
            log.info("开始初始化阿里云RocketMQ消费者...");

            // 订阅简历解析Topic
            consumer.subscribe("RESUME_PARSE_TOPIC", "resume_parse", messageListener);
            log.info("订阅Topic成功: RESUME_PARSE_TOPIC");

            // 订阅文件上传Topic
            consumer.subscribe("FILE_UPLOAD_TOPIC", "file_upload", messageListener);
            log.info("订阅Topic成功: FILE_UPLOAD_TOPIC");

            // 订阅测试Topic
            consumer.subscribe("TEST_TOPIC", "test", messageListener);
            log.info("订阅Topic成功: TEST_TOPIC");

            // 启动消费者
            consumer.start();
            log.info("阿里云RocketMQ消费者启动成功");

        } catch (Exception e) {
            log.error("阿里云RocketMQ消费者初始化失败", e);
            throw new RuntimeException("RocketMQ消费者初始化失败", e);
        }
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void shutdown() {
        try {
            if (consumer != null) {
                consumer.shutdown();
                log.info("阿里云RocketMQ消费者已关闭");
            }
        } catch (Exception e) {
            log.error("关闭阿里云RocketMQ消费者时发生异常", e);
        }
    }

    /**
     * 动态订阅新的Topic
     */
    public boolean subscribeNewTopic(String topic, String tag) {
        try {
            consumer.subscribe(topic, tag, messageListener);
            log.info("动态订阅Topic成功: {} - {}", topic, tag);
            return true;
        } catch (Exception e) {
            log.error("动态订阅Topic失败: {} - {}", topic, tag, e);
            return false;
        }
    }

    /**
     * 获取消费者状态
     */
    public boolean isConsumerRunning() {
        try {
            return consumer != null;
        } catch (Exception e) {
            log.error("检查消费者状态失败", e);
            return false;
        }
    }
}
