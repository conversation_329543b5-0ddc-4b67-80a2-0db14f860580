package com.tinyzk.user.center.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 阿里云RocketMQ消息监听器
 * 基于官方TCP示例的最佳实践
 */
@Slf4j
public class AliyunMessageListener implements MessageListener {

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        try {
            // 按照官方Demo的方式处理消息
            log.info("{} 接收到消息, Topic: {}, Tag: {}, MsgId: {}", 
                    new Date(), message.getTopic(), message.getTag(), message.getMsgID());
            
            // 获取消息内容
            String messageBody = new String(message.getBody());
            log.info("消息内容: {}", messageBody);
            
            // 根据Topic和Tag处理不同类型的消息
            boolean processResult = processMessage(message.getTopic(), message.getTag(), messageBody);
            
            if (processResult) {
                // 消息处理成功，提交消息
                log.info("消息处理成功, MsgId: {}", message.getMsgID());
                return Action.CommitMessage;
            } else {
                // 消息处理失败，重新消费
                log.warn("消息处理失败，将重新消费, MsgId: {}", message.getMsgID());
                return Action.ReconsumeLater;
            }
            
        } catch (Exception e) {
            log.error("消息处理异常, MsgId: {}, 错误信息: {}", message.getMsgID(), e.getMessage(), e);
            // 发生异常时，根据异常类型决定是否重新消费
            return Action.ReconsumeLater;
        }
    }

    /**
     * 处理不同类型的消息
     */
    private boolean processMessage(String topic, String tag, String messageBody) {
        try {
            switch (topic) {
                case "RESUME_PARSE_TOPIC":
                    return processResumeParseMessage(tag, messageBody);
                case "FILE_UPLOAD_TOPIC":
                    return processFileUploadMessage(tag, messageBody);
                case "TEST_TOPIC":
                    return processTestMessage(tag, messageBody);
                default:
                    log.warn("未知的Topic类型: {}", topic);
                    return false;
            }
        } catch (Exception e) {
            log.error("处理消息时发生异常, Topic: {}, Tag: {}", topic, tag, e);
            return false;
        }
    }

    /**
     * 处理简历解析消息
     */
    private boolean processResumeParseMessage(String tag, String messageBody) {
        log.info("处理简历解析消息: Tag={}, Body={}", tag, messageBody);
        
        try {
            // TODO: 实现具体的简历解析逻辑
            // 这里可以调用简历解析服务
            
            // 模拟处理时间
            Thread.sleep(100);
            
            log.info("简历解析消息处理完成");
            return true;
        } catch (Exception e) {
            log.error("简历解析消息处理失败", e);
            return false;
        }
    }

    /**
     * 处理文件上传消息
     */
    private boolean processFileUploadMessage(String tag, String messageBody) {
        log.info("处理文件上传消息: Tag={}, Body={}", tag, messageBody);
        
        try {
            // TODO: 实现具体的文件上传处理逻辑
            
            // 模拟处理时间
            Thread.sleep(50);
            
            log.info("文件上传消息处理完成");
            return true;
        } catch (Exception e) {
            log.error("文件上传消息处理失败", e);
            return false;
        }
    }

    /**
     * 处理测试消息
     */
    private boolean processTestMessage(String tag, String messageBody) {
        log.info("处理测试消息: Tag={}, Body={}", tag, messageBody);
        
        try {
            // 测试消息直接返回成功
            log.info("测试消息处理完成");
            return true;
        } catch (Exception e) {
            log.error("测试消息处理失败", e);
            return false;
        }
    }
}
