package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.service.MigratedMessageProducerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.tinyzk.user.center.dto.SendResultAdapter;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息队列测试控制器
 */
@RestController
@RequestMapping("/api/test/message")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "消息队列测试", description = "用于测试RocketMQ消息队列功能")
public class MessageTestController {

    private final MigratedMessageProducerService messageProducerService;

    @PostMapping("/resume-parse")
    @Operation(summary = "测试简历解析消息发送")
    public Map<String, Object> testResumeParseMessage(@RequestParam(defaultValue = "测试简历解析") String content) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构造测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("content", content);
            message.put("timestamp", LocalDateTime.now());
            message.put("testType", "RESUME_PARSE_TEST");
            
            // 发送消息
            messageProducerService.sendResumeParseMessage(message);
            
            result.put("success", true);
            result.put("message", "简历解析消息发送成功");
            result.put("data", message);
            
            log.info("简历解析测试消息发送成功: {}", message);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "消息发送失败: " + e.getMessage());
            log.error("简历解析测试消息发送失败", e);
        }
        
        return result;
    }

    @PostMapping("/file-upload")
    @Operation(summary = "测试文件上传消息发送")
    public Map<String, Object> testFileUploadMessage(@RequestParam(defaultValue = "测试文件上传") String fileName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构造测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("fileName", fileName);
            message.put("fileSize", 1024);
            message.put("timestamp", LocalDateTime.now());
            message.put("testType", "FILE_UPLOAD_TEST");
            
            // 发送消息
            messageProducerService.sendFileUploadMessage(message);
            
            result.put("success", true);
            result.put("message", "文件上传消息发送成功");
            result.put("data", message);
            
            log.info("文件上传测试消息发送成功: {}", message);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "消息发送失败: " + e.getMessage());
            log.error("文件上传测试消息发送失败", e);
        }
        
        return result;
    }

    @PostMapping("/general")
    @Operation(summary = "测试通用消息发送")
    public Map<String, Object> testGeneralMessage(
            @RequestParam(defaultValue = "TEST_TOPIC") String topic,
            @RequestParam(defaultValue = "TEST") String tag,
            @RequestParam(defaultValue = "测试通用消息") String content) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构造测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("content", content);
            message.put("timestamp", LocalDateTime.now());
            message.put("testType", "GENERAL_TEST");
            
            // 发送消息
            messageProducerService.sendGeneralMessage(topic, tag, message);
            
            result.put("success", true);
            result.put("message", "通用消息发送成功");
            result.put("data", message);
            result.put("topic", topic);
            result.put("tag", tag);
            
            log.info("通用测试消息发送成功: topic={}, tag={}, message={}", topic, tag, message);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "消息发送失败: " + e.getMessage());
            log.error("通用测试消息发送失败", e);
        }
        
        return result;
    }

    @PostMapping("/sync")
    @Operation(summary = "测试同步消息发送")
    public Map<String, Object> testSyncMessage(
            @RequestParam(defaultValue = "SYNC_TEST_TOPIC") String topic,
            @RequestParam(defaultValue = "SYNC") String tag,
            @RequestParam(defaultValue = "测试同步消息") String content) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构造测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("content", content);
            message.put("timestamp", LocalDateTime.now());
            message.put("testType", "SYNC_TEST");
            
            // 同步发送消息
            SendResultAdapter sendResult = messageProducerService.sendMessageSync(topic, tag, message);
            
            result.put("success", true);
            result.put("message", "同步消息发送成功");
            result.put("data", message);
            result.put("sendResult", Map.of(
                "messageId", sendResult.getMsgId(),
                "queueId", sendResult.getMessageQueue().getQueueId(),
                "queueOffset", sendResult.getQueueOffset(),
                "status", sendResult.getSendStatus().name()
            ));
            
            log.info("同步测试消息发送成功: topic={}, tag={}, result={}", topic, tag, sendResult);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "消息发送失败: " + e.getMessage());
            log.error("同步测试消息发送失败", e);
        }
        
        return result;
    }

    @GetMapping("/status")
    @Operation(summary = "获取消息队列状态")
    public Map<String, Object> getMessageQueueStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("message", "消息队列状态正常");
            result.put("topics", Map.of(
                "RESUME_PARSE_TOPIC", "简历解析主题",
                "FILE_UPLOAD_TOPIC", "文件上传主题",
                "GENERAL_TOPIC", "通用主题"
            ));
            result.put("timestamp", LocalDateTime.now());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            log.error("获取消息队列状态失败", e);
        }
        
        return result;
    }
}
