package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.service.AliyunConsumerService;
import com.tinyzk.user.center.service.AliyunMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云RocketMQ测试控制器
 * 基于官方TCP示例的验证接口
 */
@RestController
@RequestMapping("/api/test/aliyun-rocketmq")
@Tag(name = "阿里云RocketMQ测试", description = "基于官方Demo的RocketMQ连接测试接口")
@Slf4j
public class AliyunRocketMQTestController {

    private final AliyunMessageService messageService;
    private final AliyunConsumerService consumerService;

    public AliyunRocketMQTestController(AliyunMessageService messageService, 
                                       AliyunConsumerService consumerService) {
        this.messageService = messageService;
        this.consumerService = consumerService;
    }

    /**
     * 测试发送消息
     * 基于官方Demo的发送方式
     */
    @PostMapping("/send-test-message")
    @Operation(summary = "发送测试消息", description = "使用阿里云官方API发送测试消息")
    public Map<String, Object> sendTestMessage(@RequestParam(defaultValue = "Hello Aliyun RocketMQ!") String message) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始发送测试消息: {}", message);
            
            boolean success = messageService.sendTestMessage(message);
            
            result.put("success", success);
            result.put("message", success ? "消息发送成功" : "消息发送失败");
            result.put("messageBody", message);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("测试消息发送结果: {}", success);
            
        } catch (Exception e) {
            log.error("发送测试消息异常", e);
            result.put("success", false);
            result.put("message", "发送消息异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试发送简历解析消息
     */
    @PostMapping("/send-resume-parse-message")
    @Operation(summary = "发送简历解析消息", description = "测试简历解析消息发送")
    public Map<String, Object> sendResumeParseMessage(@RequestParam String resumeData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始发送简历解析消息");
            
            boolean success = messageService.sendResumeParseMessage(resumeData);
            
            result.put("success", success);
            result.put("message", success ? "简历解析消息发送成功" : "简历解析消息发送失败");
            result.put("resumeData", resumeData);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("发送简历解析消息异常", e);
            result.put("success", false);
            result.put("message", "发送消息异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量发送测试消息
     */
    @PostMapping("/send-batch-messages")
    @Operation(summary = "批量发送测试消息", description = "测试批量消息发送功能")
    public Map<String, Object> sendBatchMessages(@RequestParam(defaultValue = "5") int count) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始批量发送测试消息，数量: {}", count);
            
            String[] messages = new String[count];
            for (int i = 0; i < count; i++) {
                messages[i] = "批量测试消息 #" + (i + 1) + " - " + System.currentTimeMillis();
            }
            
            int successCount = messageService.sendBatchMessages(
                AliyunMessageService.TEST_TOPIC, 
                AliyunMessageService.TEST_TAG, 
                messages
            );
            
            result.put("success", successCount > 0);
            result.put("totalCount", count);
            result.put("successCount", successCount);
            result.put("failureCount", count - successCount);
            result.put("message", String.format("批量发送完成，成功: %d, 失败: %d", successCount, count - successCount));
            
        } catch (Exception e) {
            log.error("批量发送消息异常", e);
            result.put("success", false);
            result.put("message", "批量发送异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查RocketMQ连接状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查连接状态", description = "检查生产者和消费者的连接状态")
    public Map<String, Object> checkStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean producerRunning = messageService.isProducerRunning();
            boolean consumerRunning = consumerService.isConsumerRunning();
            
            result.put("producerRunning", producerRunning);
            result.put("consumerRunning", consumerRunning);
            result.put("overallStatus", producerRunning && consumerRunning ? "正常" : "异常");
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("RocketMQ状态检查 - Producer: {}, Consumer: {}", producerRunning, consumerRunning);
            
        } catch (Exception e) {
            log.error("检查RocketMQ状态异常", e);
            result.put("error", e.getMessage());
            result.put("overallStatus", "检查异常");
        }
        
        return result;
    }

    /**
     * 动态订阅新Topic
     */
    @PostMapping("/subscribe-topic")
    @Operation(summary = "动态订阅Topic", description = "动态订阅新的Topic")
    public Map<String, Object> subscribeTopic(@RequestParam String topic, 
                                             @RequestParam(defaultValue = "*") String tag) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = consumerService.subscribeNewTopic(topic, tag);
            
            result.put("success", success);
            result.put("message", success ? "订阅成功" : "订阅失败");
            result.put("topic", topic);
            result.put("tag", tag);
            
        } catch (Exception e) {
            log.error("动态订阅Topic异常", e);
            result.put("success", false);
            result.put("message", "订阅异常: " + e.getMessage());
        }
        
        return result;
    }
}
