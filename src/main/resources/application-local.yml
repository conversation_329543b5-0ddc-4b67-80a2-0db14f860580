server:
  port: 8081

spring:
  application:
    name: user-center

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************
    username: gigpal
    password: test-gigpal-2024PW
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: UserCenterHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  data:
    redis:
      host: ***********
      port: 6379
      password: Hzxiaozhuankuai2024!
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.tinyzk.user.center.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted_at
      logic-delete-value: "now()"
      logic-not-delete-value: "NULL"

# 日志配置
logging:
  config: classpath:logback-spring.xml

knife4j:
  # 开启增强配置 
  enable: true
 # 开启生产环境屏蔽
  production: false
  setting:
    language: zh_cn

# JWT Configuration
jwt:
  secret: PleaseReplaceThisWithAVeryStrongAndLongRandomSecretKey ASAP!
  expiration: 3600000 # 1 hour in milliseconds (1 * 60 * 60 * 1000)

# 阿里云RocketMQ配置 - 基于官方TCP示例
rocketmq:
  # 阿里云RocketMQ 4.0系列实例接入点（需要http://前缀）
  # 阿里云RocketMQ 5.0系列实例接入点（不需要http://前缀）
  # 当前配置为4.0系列格式
  name-server: http://MQ_INST_1708662203807002_BXfU8rhL.cn-shanghai.mq-internal.aliyuncs.com:8080
  producer:
    # 生产者GroupID（需要在控制台预先创建）
    group: GID_user-center-producer-group
    # 阿里云账号AccessKeyId（4.0系列）或实例用户名（5.0系列）
    access-key: LTAI5tL6PCY7TQgJTRw2GuV8
    # 阿里云账号AccessKeySecret（4.0系列）或实例密码（5.0系列）
    secret-key: ******************************
  consumer:
    # 消费者GroupID（需要在控制台预先创建）
    group: GID_user-center-consumer-group
    # 认证信息与生产者相同
    access-key: LTAI5tL6PCY7TQgJTRw2GuV8
    secret-key: ******************************

# 阿里云OSS配置（本地开发可以使用测试环境）
aliyun:
  oss:
    endpoint: https://oss-cn-shanghai.aliyuncs.com
    access-key-id: LTAI5tFmSq5AVvZ7XigbM3Vt
    access-key-secret: ******************************
    bucket-name: tzk-resume
    connection-timeout: 3000
    socket-timeout: 30000
    max-connections: 50
    max-error-retry: 3
    use-internal-endpoint: false

# 简历解析配置
resume:
  parse:
    api-url: http://192.168.0.124:8000
    timeout: 30000
    max-retries: 3
    supported-file-types: doc,docx,pdf
    max-file-size: 10485760  # 10MB

# Resilience4j配置
resilience4j:
  # 熔断器配置
  circuitbreaker:
    instances:
      third-party-api:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 60s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 10s

  # 限流器配置
  ratelimiter:
    instances:
      message-producer:
        limit-for-period: 50
        limit-refresh-period: 1s
        timeout-duration: 100ms
      message-consumer:
        limit-for-period: 30
        limit-refresh-period: 1s
        timeout-duration: 100ms

  # 重试配置
  retry:
    instances:
      third-party-api:
        max-attempts: 3
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.net.SocketTimeoutException
          - java.net.ConnectException
          - org.springframework.web.client.HttpServerErrorException